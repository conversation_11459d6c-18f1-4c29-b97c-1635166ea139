import React, { useState, useEffect } from 'react';
import { languageValidationService, type LanguageStats } from '../services/languageValidationService';

interface LanguageComplianceMonitorProps {
    className?: string;
}

const LanguageComplianceMonitor: React.FC<LanguageComplianceMonitorProps> = ({ className = '' }) => {
    const [stats, setStats] = useState<LanguageStats | null>(null);
    const [isExpanded, setIsExpanded] = useState(false);
    const [showReport, setShowReport] = useState(false);

    // Mise à jour des statistiques toutes les 5 secondes
    useEffect(() => {
        const updateStats = () => {
            const currentStats = languageValidationService.getStats();
            setStats(currentStats);
        };

        updateStats();
        const interval = setInterval(updateStats, 5000);

        return () => clearInterval(interval);
    }, []);

    if (!stats || stats.totalValidations === 0) {
        return null;
    }

    const frenchPercentage = (stats.frenchResponses / stats.totalValidations) * 100;
    const problematicModels = languageValidationService.getProblematicModels();
    
    // Déterminer la couleur de l'indicateur de conformité
    const getComplianceColor = (percentage: number) => {
        if (percentage >= 90) return 'text-green-600 bg-green-100';
        if (percentage >= 70) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const complianceColor = getComplianceColor(frenchPercentage);

    return (
        <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
            {/* En-tête compact */}
            <div 
                className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${complianceColor}`}>
                        🇫🇷 {frenchPercentage.toFixed(1)}%
                    </div>
                    <span className="text-sm font-medium text-gray-700">
                        Conformité Linguistique
                    </span>
                    {problematicModels.length > 0 && (
                        <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                            {problematicModels.length} modèle{problematicModels.length > 1 ? 's' : ''} problématique{problematicModels.length > 1 ? 's' : ''}
                        </span>
                    )}
                </div>
                <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                        {stats.totalValidations} validation{stats.totalValidations > 1 ? 's' : ''}
                    </span>
                    <svg 
                        className={`w-4 h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Contenu détaillé */}
            {isExpanded && (
                <div className="border-t border-gray-200 p-4 space-y-4">
                    {/* Statistiques globales */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">{stats.frenchResponses}</div>
                            <div className="text-xs text-gray-500">Français</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-red-600">{stats.englishResponses}</div>
                            <div className="text-xs text-gray-500">Anglais</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-yellow-600">{stats.mixedResponses}</div>
                            <div className="text-xs text-gray-500">Mixte</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-600">{stats.unknownResponses}</div>
                            <div className="text-xs text-gray-500">Inconnu</div>
                        </div>
                    </div>

                    {/* Confiance moyenne */}
                    <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-700">Confiance moyenne</span>
                            <span className="text-sm font-bold text-gray-900">
                                {stats.averageConfidence.toFixed(1)}%
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min(stats.averageConfidence, 100)}%` }}
                            ></div>
                        </div>
                    </div>

                    {/* Modèles problématiques */}
                    {problematicModels.length > 0 && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <h4 className="text-sm font-medium text-red-800 mb-2">
                                ⚠️ Modèles Problématiques
                            </h4>
                            <div className="space-y-2">
                                {problematicModels.slice(0, 3).map((model, index) => (
                                    <div key={index} className="flex justify-between items-center text-xs">
                                        <span className="text-red-700 font-mono truncate max-w-[200px]">
                                            {model.modelId.split('/').pop()}
                                        </span>
                                        <div className="flex space-x-2">
                                            <span className="text-red-600">
                                                {model.frenchPercentage.toFixed(1)}% FR
                                            </span>
                                            <span className="text-gray-500">
                                                ({model.issues}/{model.total})
                                            </span>
                                        </div>
                                    </div>
                                ))}
                                {problematicModels.length > 3 && (
                                    <div className="text-xs text-red-600 text-center">
                                        +{problematicModels.length - 3} autres modèles
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200">
                        <button
                            onClick={() => setShowReport(!showReport)}
                            className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                        >
                            {showReport ? 'Masquer' : 'Voir'} le rapport détaillé
                        </button>
                        <button
                            onClick={() => {
                                languageValidationService.resetStats();
                                setStats(languageValidationService.getStats());
                            }}
                            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                        >
                            Réinitialiser les stats
                        </button>
                        <button
                            onClick={() => {
                                const report = languageValidationService.generateComplianceReport();
                                navigator.clipboard.writeText(report);
                                // Vous pourriez ajouter une notification toast ici
                            }}
                            className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                        >
                            Copier le rapport
                        </button>
                    </div>

                    {/* Rapport détaillé */}
                    {showReport && (
                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4">
                            <h4 className="text-sm font-medium text-gray-800 mb-3">
                                📊 Rapport de Conformité Détaillé
                            </h4>
                            <div className="text-xs text-gray-700 space-y-2">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <strong>Validations totales:</strong> {stats.totalValidations}
                                    </div>
                                    <div>
                                        <strong>Taux de conformité:</strong> {frenchPercentage.toFixed(2)}%
                                    </div>
                                </div>
                                
                                {Object.keys(stats.modelStats).length > 0 && (
                                    <div className="mt-4">
                                        <strong>Statistiques par modèle:</strong>
                                        <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                                            {Object.entries(stats.modelStats)
                                                .sort(([,a], [,b]) => (b.french / b.total) - (a.french / a.total))
                                                .map(([modelId, modelStats]) => {
                                                    const modelFrenchPercentage = (modelStats.french / modelStats.total) * 100;
                                                    return (
                                                        <div key={modelId} className="flex justify-between items-center py-1 px-2 bg-white rounded">
                                                            <span className="font-mono text-xs truncate max-w-[150px]">
                                                                {modelId.split('/').pop()}
                                                            </span>
                                                            <div className="flex space-x-2 text-xs">
                                                                <span className={`font-medium ${modelFrenchPercentage >= 80 ? 'text-green-600' : 'text-red-600'}`}>
                                                                    {modelFrenchPercentage.toFixed(1)}%
                                                                </span>
                                                                <span className="text-gray-500">
                                                                    ({modelStats.french}/{modelStats.total})
                                                                </span>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default LanguageComplianceMonitor;
