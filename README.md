# Studio-Agentique-Roony

🤖 **Roony** - Studio Agentique de Workflow IA

## Description

Roony est un studio agentique avancé qui guide les utilisateurs à travers un processus structuré de résolution de problèmes complexes en utilisant l'intelligence artificielle. L'application propose un workflow en 15 étapes pour analyser, innover et générer des solutions sur mesure.

## Fonctionnalités

### 🎨 **Interface & Expérience Utilisateur**
- ✨ **Layout professionnel 3 colonnes** : Organisation optimale de l'espace de travail
- 🧠 **Mascotte intelligente** qui s'affiche pendant les phases de raisonnement
- 📊 **Suivi de progression** en temps réel avec accordéons interactifs
- � **Guidance contextuelle** : Conseils adaptatifs selon l'étape du workflow
- 🎯 **Suggestions d'interaction** : Propositions intelligentes pour enrichir la conversation
- 📱 **Footer slide interactif** avec liens sociaux

### �🔄 **Workflow & Intelligence**
- 🚀 **Workflow structuré** en 15 étapes pour résolution de problèmes complexes
- 🎯 **Génération de prompts optimisés** avec méta-analyse détaillée
- 🤖 **Sélection automatique de modèles IA** selon le contexte et la complexité
- 📈 **Engagement utilisateur continu** : Actions claires à chaque étape

### 🔧 **Système de Mise à Jour Automatique**
- ⏰ **Mise à jour automatique des modèles IA** toutes les 24 heures
- � **Notifications intelligentes** : Alertes discrètes des nouvelles mises à jour
- 📊 **Monitoring en temps réel** : Statut des modèles et prochaine mise à jour
- 🆕 **Détection de nouveaux modèles** : Identification automatique des modèles gratuits

### 🎛️ **Actions Post-Workflow**
- 🔄 **Nouveau Workflow** : Redémarrage rapide pour un nouveau problème
- ✏️ **Affiner le Prompt** : Itération sur la solution générée
- 📤 **Export multi-format** : Sauvegarde en .md, .json, .txt
- 💾 **Copie rapide** : Bouton de copie intégré

## 🏗️ Architecture de l'Interface

### Layout 3 Colonnes Optimisé
```
┌─────────────────────────────────────────────────────────────┐
│                        HEADER                               │
├─────────────┬─────────────────────────┬─────────────────────┤
│   COLONNE   │       COLONNE           │      COLONNE        │
│   GAUCHE    │       CENTRE            │      DROITE         │
│   (25%)     │       (50%)             │      (25%)          │
│             │                         │                     │
│ • Progression│   Chat Interface        │ • Raisonnement      │
│   Workflow  │   + Input Utilisateur   │   Agent IA          │
│ • Guidance  │                         │ • Modèles IA        │
│   Contextuelle│   (ZONE PRINCIPALE)     │   & Monitoring      │
│ • Suggestions│                         │ • Mise à jour       │
│   Interaction│                         │   Automatique       │
└─────────────┴─────────────────────────┴─────────────────────┘
```

### Composants Intelligents
- **Accordéons Interactifs** : Composants pliables/dépliables pour optimiser l'espace
- **Notifications Toast** : Système d'alertes non-intrusives avec animations
- **Guidance Adaptive** : Conseils contextuels selon l'étape du workflow
- **Monitoring Temps Réel** : Suivi automatique des modèles IA et mises à jour

## Technologies utilisées

- **React 19** + **TypeScript**
- **Vite.js** pour le développement et le build
- **Tailwind CSS** pour le styling
- **GSAP** pour les animations
- **OpenRouter AI** pour l'intelligence artificielle

## Installation

```bash
# Cloner le repository
git clone https://github.com/cisco-03/Studio-Agentique-Roony.git

# Installer les dépendances
npm install

# Lancer en mode développement
npm run dev
```

## Configuration

Créer un fichier `.env.local` avec vos clés API :

```env
VITE_API_KEY=sk-or-v1-votre-clé-openrouter
```

## 🆕 Nouvelles Fonctionnalités (Version 3.0)

### 🎯 Améliorations Majeures Récentes
- **Interface Révolutionnée** : Passage d'un layout vertical écrasé à un système 3 colonnes professionnel
- **Engagement Utilisateur Optimisé** : Fini les utilisateurs "en plan" après génération du prompt
- **Système Automatique** : Mise à jour des modèles IA toutes les 24h sans intervention utilisateur
- **Notifications Intelligentes** : Alertes discrètes avec animations et auto-fermeture
- **Composants Compacts** : Accordéons interactifs pour maximiser l'espace de conversation

### 📈 Impact des Améliorations
- **Zone de conversation 2x plus grande** : Lisibilité maximale des échanges
- **Actions claires post-workflow** : Boutons "Nouveau Workflow" et "Affiner ce Prompt"
- **Mises à jour transparentes** : Modèles toujours à jour automatiquement
- **Expérience fluide** : Guidance contextuelle et suggestions intelligentes

## 🚀 Configuration Requise

Pour fonctionner, cette application **doit** avoir accès à une clé API OpenRouter via une variable d'environnement.

### 1. Obtenir une Clé API OpenRouter

- Rendez-vous sur [OpenRouter.ai](https://openrouter.ai/).
- Créez un compte ou connectez-vous.
- Accédez à votre page de clés (Keys) pour en générer une nouvelle.

### 2. Configuration de l'Environnement

L'application est conçue pour récupérer la clé API directement de l'environnement d'exécution. Vous devez configurer une variable d'environnement nommée `API_KEY` avec votre clé OpenRouter.

---

## 🤖 Fonctionnement de la Sélection de Modèles via OpenRouter

L'un des points forts de cette application est sa capacité à choisir le meilleur outil pour chaque tâche. Voici comment cela fonctionne :

1.  **Une Seule Clé, Plusieurs Modèles** : Votre unique clé API OpenRouter nous donne accès à des dizaines de modèles d'IA différents.

2.  **Tâches Catégorisées** : Chaque étape de notre workflow (défini dans `constants.ts`) est associée à un type de tâche spécifique : `'analyse'`, `'génération'`, `'validation'`, ou `'synthèse'`.

3.  **Sélection Intelligente** :
    - Le fichier `constants.ts` contient un objet `OPENROUTER_MODELS` qui liste les modèles gratuits les plus performants pour chaque type de tâche.
    - Lorsque nous arrivons à une nouvelle étape, le service (`services/geminiService.ts`) consulte cette liste et choisit aléatoirement un des modèles appropriés pour la tâche en cours.

4.  **Transparence Totale** : Pour que vous sachiez toujours quel "cerveau" est à l'œuvre, l'agent IA annonce ses choix dans l' "Espace de Raisonnement".

---

## 🔌 Extensibilité : Connexion aux Données (Vision Future)

L'application est conçue pour être extensible. À l'avenir, l'agent pourra se connecter à des sources de données externes (bases de données, API, etc.) pour enrichir son analyse.

### Architecture en Place

-   **Configuration Centralisée** : Le fichier `constants.ts` contient une liste `DATA_SOURCES`. C'est ici que vous pouvez déclarer de nouvelles sources de données.
-   **Service Dédié** : Le fichier `services/dataSourceService.ts` contient la logique pour interroger ces sources. Actuellement, il s'agit d'une simulation, mais il est prêt à être connecté à de vraies API.

### Comment Ajouter une Nouvelle Source de Données ?

1.  **Déclarez la source** dans `DATA_SOURCES` dans `constants.ts`.
2.  **Implémentez la logique d'appel** dans `services/dataSourceService.ts`.

L'agent pourra alors être instruit de "rechercher des informations dans MCP Server Context7" et utilisera ce service pour obtenir des données en temps réel.

## 🎮 Guide d'Utilisation

### Démarrage Rapide
1. **Lancez l'application** et décrivez votre problème complexe
2. **Suivez le workflow** : L'agent vous guide à travers 15 étapes structurées
3. **Interagissez activement** : Utilisez les suggestions d'interaction pour enrichir l'analyse
4. **Consultez la guidance** : Cliquez sur les accordéons pour des conseils contextuels
5. **Récupérez votre solution** : Prompt optimisé + méta-analyse détaillée

### Fonctionnalités Avancées
- **Accordéons Informatifs** : Cliquez sur "Progression" et "Conseils" pour plus de détails
- **Suggestions Intelligentes** : Boutons cliquables pour faciliter l'interaction
- **Monitoring Automatique** : Suivi en temps réel des modèles IA et mises à jour
- **Actions Post-Génération** : "Nouveau Workflow" ou "Affiner ce Prompt"
- **Export Flexible** : Sauvegarde en .md, .json ou .txt

### Interface 3 Colonnes
- **Colonne Gauche** : Progression, guidance et suggestions d'interaction
- **Colonne Centre** : Zone principale de conversation avec l'agent IA
- **Colonne Droite** : Raisonnement de l'agent et monitoring des modèles

## Déploiement

L'application est optimisée pour le déploiement sur Netlify.

## Auteur

**FlexoDiv** - Ingénieur en Prompting et Développement IA

- 🌐 [Portfolio](https://flexodiv.netlify.app/)
- 📧 [Email](mailto:<EMAIL>)
- 💼 [LinkedIn](https://www.linkedin.com/in/flexodiv-engineering-prompting-982582203)
- 🎥 [YouTube](https://www.youtube.com/@flexodiv)

## Licence

© 2025 FlexoDiv - Tous droits réservés